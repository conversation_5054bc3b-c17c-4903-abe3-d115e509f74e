import { useEffect } from 'react';

interface PageTitleProps {
  title: string;
}

const PageTitle: React.FC<PageTitleProps> = ({ title }) => {
  useEffect(() => {
    // Update document title
    document.title = title;
    
    // Also update the title meta tag if it exists
    const titleMeta = document.querySelector('meta[property="og:title"]');
    if (titleMeta) {
      titleMeta.setAttribute('content', title);
    }
    
    const twitterTitleMeta = document.querySelector('meta[name="twitter:title"]');
    if (twitterTitleMeta) {
      twitterTitleMeta.setAttribute('content', title);
    }
  }, [title]);

  return null; // This component doesn't render anything
};

export default PageTitle;
