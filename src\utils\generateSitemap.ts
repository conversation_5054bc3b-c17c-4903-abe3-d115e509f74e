// Utility to generate dynamic sitemap based on project data
import { BASE_URL } from './seo';

export interface SitemapUrl {
  loc: string;
  lastmod: string;
  changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: number;
  images?: Array<{
    loc: string;
    title: string;
    caption: string;
  }>;
}

export const generateSitemapUrls = (projects: any[]): SitemapUrl[] => {
  const currentDate = new Date().toISOString().split('T')[0];
  
  const urls: SitemapUrl[] = [
    // Homepage
    {
      loc: BASE_URL,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 1.0,
      images: [{
        loc: `${BASE_URL}/og-image.jpg`,
        title: 'Productiehuis Ouroboros - Een podium voor kunst met impact',
        caption: 'Productiehuis Ouroboros is een gedreven productiehuis dat zich focust op aanstormend talent in de podiumkunsten.'
      }]
    },
    
    // About section
    {
      loc: `${BASE_URL}/#about`,
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: 0.8
    },
    
    // Projects section
    {
      loc: `${BASE_URL}/#projects`,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: 0.9
    },
    
    // Contact section
    {
      loc: `${BASE_URL}/#contact`,
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: 0.7
    }
  ];

  // Add individual project pages
  projects.forEach(project => {
    const imageUrl = project.imageUrl.startsWith('http') 
      ? project.imageUrl 
      : `${BASE_URL}${project.imageUrl}`;
      
    urls.push({
      loc: `${BASE_URL}/projects/${project.id}`,
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: 0.8,
      images: [{
        loc: imageUrl,
        title: project.title,
        caption: `${project.title} - Een theaterproductie van Productiehuis Ouroboros`
      }]
    });
  });

  return urls;
};

export const generateSitemapXML = (urls: SitemapUrl[]): string => {
  const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>';
  const urlsetOpen = '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">';
  const urlsetClose = '</urlset>';

  const urlElements = urls.map(url => {
    let urlXml = `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>`;

    if (url.images && url.images.length > 0) {
      url.images.forEach(image => {
        urlXml += `
    <image:image>
      <image:loc>${image.loc}</image:loc>
      <image:title>${image.title}</image:title>
      <image:caption>${image.caption}</image:caption>
    </image:image>`;
      });
    }

    urlXml += `
  </url>`;

    return urlXml;
  }).join('\n');

  return `${xmlHeader}
${urlsetOpen}
${urlElements}
${urlsetClose}`;
};
