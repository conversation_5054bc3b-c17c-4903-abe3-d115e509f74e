
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import NavBar from "../components/NavBar";
import Hero from "../components/Hero";
import About from "../components/About";
import Projects from "../components/Projects";
import Contact from "../components/Contact";
import Footer from "../components/Footer";
import SEOHead from "../components/SEOHead";
import { useSEO } from "../hooks/useSEO";

const Index = () => {
  const location = useLocation();
  const { seoData, structuredData, canonical } = useSEO({ pageType: 'home' });

  // Handle navigation state and hash navigation when component mounts
  useEffect(() => {
    // Check if we have a scrollTo state from navigation
    const state = location.state as { scrollTo?: string } | null;

    if (state?.scrollTo) {
      // Small delay to ensure the page has rendered
      setTimeout(() => {
        const element = document.getElementById(state.scrollTo);
        if (element) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      }, 100);
    } else {
      // Handle hash navigation when component mounts
      const hash = window.location.hash;
      if (hash) {
        // Small delay to ensure the page has rendered
        setTimeout(() => {
          const element = document.getElementById(hash.substring(1));
          if (element) {
            element.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
            });
          }
        }, 100);
      }
    }
  }, [location]);

  return (
    <div className="flex flex-col min-h-screen">
      <SEOHead
        seoData={seoData}
        structuredData={structuredData}
        canonical={canonical}
      />
      <NavBar />
      <Hero />
      <About />
      <Projects />
      <Contact />
      <Footer />
    </div>
  );
};

export default Index;
