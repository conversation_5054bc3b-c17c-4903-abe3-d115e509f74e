import React from 'react';
import { Helmet } from 'react-helmet-async';
import { SEOData, StructuredData, generateMetaTags, DEFAULT_SEO } from '../utils/seo';

interface SEOHeadProps {
  seoData?: Partial<SEOData>;
  structuredData?: StructuredData[];
  noIndex?: boolean;
  canonical?: string;
}

const SEOHead: React.FC<SEOHeadProps> = ({ 
  seoData = {}, 
  structuredData = [], 
  noIndex = false,
  canonical 
}) => {
  // Merge with default SEO data
  const finalSEOData: SEOData = {
    ...DEFAULT_SEO,
    ...seoData
  };

  // Generate meta tags
  const metaTags = generateMetaTags(finalSEOData);

  return (
    <Helmet>
      {/* Title */}
      <title>{finalSEOData.title}</title>

      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}

      {/* Robots */}
      <meta 
        name="robots" 
        content={noIndex ? 'noindex, nofollow' : 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1'} 
      />

      {/* Meta tags */}
      {metaTags.map((tag, index) => {
        if ('name' in tag) {
          return <meta key={index} name={tag.name} content={tag.content} />;
        } else if ('property' in tag) {
          return <meta key={index} property={tag.property} content={tag.content} />;
        }
        return null;
      })}

      {/* Open Graph Image dimensions */}
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={`${finalSEOData.title} - Productiehuis Ouroboros`} />

      {/* Twitter Image Alt */}
      <meta name="twitter:image:alt" content={`${finalSEOData.title} - Productiehuis Ouroboros`} />

      {/* Additional meta tags */}
      <meta name="author" content="Productiehuis Ouroboros" />
      <meta name="language" content="Dutch" />
      <meta name="geo.region" content="BE" />
      <meta name="geo.placename" content="België" />

      {/* Structured Data */}
      {structuredData.map((data, index) => (
        <script key={index} type="application/ld+json">
          {JSON.stringify(data)}
        </script>
      ))}
    </Helmet>
  );
};

export default SEOHead;
