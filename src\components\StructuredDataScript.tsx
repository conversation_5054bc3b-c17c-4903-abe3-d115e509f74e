import React from 'react';
import { StructuredData } from '../utils/seo';

interface StructuredDataScriptProps {
  data: StructuredData[];
}

const StructuredDataScript: React.FC<StructuredDataScriptProps> = ({ data }) => {
  return (
    <>
      {data.map((structuredData, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData, null, 2)
          }}
        />
      ))}
    </>
  );
};

export default StructuredDataScript;
